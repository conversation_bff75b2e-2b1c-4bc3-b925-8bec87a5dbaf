<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_data_binding" modulePackage="com.sdwu.kotlin" filePath="app\src\main\res\layout\activity_data_binding.xml" directory="layout" isMerge="false" isBindingData="true" rootNodeType="android.widget.LinearLayout"><Variables declared="true" type="com.sdwu.kotlin.viewmodel.DataBindingViewModel" name="viewModel"><location startLine="7" startOffset="8" endLine="9" endOffset="67"/></Variables><Targets><Target tag="layout/activity_data_binding_0" view="LinearLayout"><Expressions/><location startLine="12" startOffset="4" endLine="96" endOffset="18"/></Target><Target id="@+id/et_name" view="EditText"><Expressions/><location startLine="30" startOffset="8" endLine="36" endOffset="48"/></Target><Target id="@+id/et_email" view="EditText"><Expressions/><location startLine="39" startOffset="8" endLine="45" endOffset="48"/></Target><Target id="@+id/tv_user_info" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="57" endOffset="48"/></Target><Target id="@+id/btn_save" view="Button"><Expressions/><location startLine="66" startOffset="12" endLine="72" endOffset="48"/></Target><Target id="@+id/btn_load" view="Button"><Expressions/><location startLine="75" startOffset="12" endLine="81" endOffset="50"/></Target></Targets></Layout>