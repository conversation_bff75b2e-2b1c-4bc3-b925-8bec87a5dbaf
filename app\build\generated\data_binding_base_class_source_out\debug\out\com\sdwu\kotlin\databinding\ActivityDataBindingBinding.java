// Generated by data binding compiler. Do not edit!
package com.sdwu.kotlin.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.Bindable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import com.sdwu.kotlin.R;
import com.sdwu.kotlin.viewmodel.DataBindingViewModel;
import java.lang.Deprecated;
import java.lang.Object;

public abstract class ActivityDataBindingBinding extends ViewDataBinding {
  @NonNull
  public final Button btnLoad;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final EditText etEmail;

  @NonNull
  public final EditText etName;

  @NonNull
  public final TextView tvUserInfo;

  @Bindable
  protected DataBindingViewModel mViewModel;

  protected ActivityDataBindingBinding(Object _bindingComponent, View _root, int _localFieldCount,
      Button btnLoad, Button btnSave, EditText etEmail, EditText etName, TextView tvUserInfo) {
    super(_bindingComponent, _root, _localFieldCount);
    this.btnLoad = btnLoad;
    this.btnSave = btnSave;
    this.etEmail = etEmail;
    this.etName = etName;
    this.tvUserInfo = tvUserInfo;
  }

  public abstract void setViewModel(@Nullable DataBindingViewModel viewModel);

  @Nullable
  public DataBindingViewModel getViewModel() {
    return mViewModel;
  }

  @NonNull
  public static ActivityDataBindingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup root, boolean attachToRoot) {
    return inflate(inflater, root, attachToRoot, DataBindingUtil.getDefaultComponent());
  }

  /**
   * This method receives DataBindingComponent instance as type Object instead of
   * type DataBindingComponent to avoid causing too many compilation errors if
   * compilation fails for another reason.
   * https://issuetracker.google.com/issues/116541301
   * @Deprecated Use DataBindingUtil.inflate(inflater, R.layout.activity_data_binding, root, attachToRoot, component)
   */
  @NonNull
  @Deprecated
  public static ActivityDataBindingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup root, boolean attachToRoot, @Nullable Object component) {
    return ViewDataBinding.<ActivityDataBindingBinding>inflateInternal(inflater, R.layout.activity_data_binding, root, attachToRoot, component);
  }

  @NonNull
  public static ActivityDataBindingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, DataBindingUtil.getDefaultComponent());
  }

  /**
   * This method receives DataBindingComponent instance as type Object instead of
   * type DataBindingComponent to avoid causing too many compilation errors if
   * compilation fails for another reason.
   * https://issuetracker.google.com/issues/116541301
   * @Deprecated Use DataBindingUtil.inflate(inflater, R.layout.activity_data_binding, null, false, component)
   */
  @NonNull
  @Deprecated
  public static ActivityDataBindingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable Object component) {
    return ViewDataBinding.<ActivityDataBindingBinding>inflateInternal(inflater, R.layout.activity_data_binding, null, false, component);
  }

  public static ActivityDataBindingBinding bind(@NonNull View view) {
    return bind(view, DataBindingUtil.getDefaultComponent());
  }

  /**
   * This method receives DataBindingComponent instance as type Object instead of
   * type DataBindingComponent to avoid causing too many compilation errors if
   * compilation fails for another reason.
   * https://issuetracker.google.com/issues/116541301
   * @Deprecated Use DataBindingUtil.bind(view, component)
   */
  @Deprecated
  public static ActivityDataBindingBinding bind(@NonNull View view, @Nullable Object component) {
    return (ActivityDataBindingBinding)bind(component, view, R.layout.activity_data_binding);
  }
}
