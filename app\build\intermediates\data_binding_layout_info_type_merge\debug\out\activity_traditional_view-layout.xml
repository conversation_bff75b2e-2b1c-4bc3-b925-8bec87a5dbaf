<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_traditional_view" modulePackage="com.sdwu.kotlin" filePath="app\src\main\res\layout\activity_traditional_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_traditional_view_0" view="LinearLayout"><Expressions/><location startLine="3" startOffset="0" endLine="113" endOffset="14"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="12" startOffset="4" endLine="21" endOffset="50"/></Target><Target id="@+id/tv_content" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="46" endOffset="39"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="49" startOffset="12" endLine="55" endOffset="43"/></Target><Target id="@+id/tv_error" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="68" endOffset="43"/></Target><Target id="@+id/btn_load" view="Button"><Expressions/><location startLine="81" startOffset="8" endLine="88" endOffset="37"/></Target><Target id="@+id/btn_save" view="Button"><Expressions/><location startLine="91" startOffset="8" endLine="98" endOffset="37"/></Target></Targets></Layout>