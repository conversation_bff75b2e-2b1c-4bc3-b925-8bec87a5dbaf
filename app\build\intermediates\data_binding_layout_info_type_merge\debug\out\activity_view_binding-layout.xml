<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_view_binding" modulePackage="com.sdwu.kotlin" filePath="app\src\main\res\layout\activity_view_binding.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_view_binding_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="3" startOffset="0" endLine="149" endOffset="51"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="12" startOffset="4" endLine="23" endOffset="41"/></Target><Target id="@+id/til_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="26" startOffset="4" endLine="42" endOffset="59"/></Target><Target id="@+id/et_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="36" startOffset="8" endLine="40" endOffset="48"/></Target><Target id="@+id/til_email" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="45" startOffset="4" endLine="61" endOffset="59"/></Target><Target id="@+id/et_email" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="55" startOffset="8" endLine="59" endOffset="50"/></Target><Target id="@+id/tv_user_info" view="TextView"><Expressions/><location startLine="64" startOffset="4" endLine="76" endOffset="51"/></Target><Target id="@+id/ll_buttons" view="LinearLayout"><Expressions/><location startLine="79" startOffset="4" endLine="107" endOffset="18"/></Target><Target id="@+id/btn_save" view="Button"><Expressions/><location startLine="90" startOffset="8" endLine="96" endOffset="44"/></Target><Target id="@+id/btn_load" view="Button"><Expressions/><location startLine="99" startOffset="8" endLine="105" endOffset="46"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="110" startOffset="4" endLine="118" endOffset="41"/></Target><Target id="@+id/tv_error" view="TextView"><Expressions/><location startLine="121" startOffset="4" endLine="132" endOffset="41"/></Target></Targets></Layout>