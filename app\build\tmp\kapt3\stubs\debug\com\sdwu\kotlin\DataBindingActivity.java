package com.sdwu.kotlin;

/**
 * DataBinding示例Activity
 * 展示DataBinding的自动数据绑定功能
 * 与ViewBindingActivity的区别：
 * 1. 使用DataBindingUtil.setContentView()
 * 2. 设置ViewModel到binding
 * 3. 设置lifecycleOwner用于LiveData观察
 * 4. 不需要手动设置点击监听器和UI更新
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u0000 \u000e2\u00020\u0001:\u0001\u000eB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\nH\u0014J\b\u0010\u000b\u001a\u00020\bH\u0014J\b\u0010\f\u001a\u00020\bH\u0002J\b\u0010\r\u001a\u00020\bH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/sdwu/kotlin/DataBindingActivity;", "Landroidx/activity/ComponentActivity;", "()V", "binding", "Lcom/sdwu/kotlin/databinding/ActivityDataBindingBinding;", "viewModel", "Lcom/sdwu/kotlin/viewmodel/DataBindingViewModel;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "setupClickListeners", "setupDataBinding", "Companion", "app_debug"})
public final class DataBindingActivity extends androidx.activity.ComponentActivity {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "DataBindingActivity";
    private com.sdwu.kotlin.databinding.ActivityDataBindingBinding binding;
    private com.sdwu.kotlin.viewmodel.DataBindingViewModel viewModel;
    @org.jetbrains.annotations.NotNull()
    public static final com.sdwu.kotlin.DataBindingActivity.Companion Companion = null;
    
    public DataBindingActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 设置DataBinding的关键配置
     */
    private final void setupDataBinding() {
    }
    
    /**
     * 设置点击监听器（简化版本）
     */
    private final void setupClickListeners() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/sdwu/kotlin/DataBindingActivity$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}