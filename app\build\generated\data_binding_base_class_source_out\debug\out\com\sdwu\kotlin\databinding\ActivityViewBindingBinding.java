// Generated by view binder compiler. Do not edit!
package com.sdwu.kotlin.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.sdwu.kotlin.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityViewBindingBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnLoad;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final TextInputEditText etEmail;

  @NonNull
  public final TextInputEditText etName;

  @NonNull
  public final LinearLayout llButtons;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout tilEmail;

  @NonNull
  public final TextInputLayout tilName;

  @NonNull
  public final TextView tvError;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvUserInfo;

  private ActivityViewBindingBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnLoad,
      @NonNull Button btnSave, @NonNull TextInputEditText etEmail,
      @NonNull TextInputEditText etName, @NonNull LinearLayout llButtons,
      @NonNull ProgressBar progressBar, @NonNull TextInputLayout tilEmail,
      @NonNull TextInputLayout tilName, @NonNull TextView tvError, @NonNull TextView tvTitle,
      @NonNull TextView tvUserInfo) {
    this.rootView = rootView;
    this.btnLoad = btnLoad;
    this.btnSave = btnSave;
    this.etEmail = etEmail;
    this.etName = etName;
    this.llButtons = llButtons;
    this.progressBar = progressBar;
    this.tilEmail = tilEmail;
    this.tilName = tilName;
    this.tvError = tvError;
    this.tvTitle = tvTitle;
    this.tvUserInfo = tvUserInfo;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityViewBindingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityViewBindingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_view_binding, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityViewBindingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_load;
      Button btnLoad = ViewBindings.findChildViewById(rootView, id);
      if (btnLoad == null) {
        break missingId;
      }

      id = R.id.btn_save;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.et_email;
      TextInputEditText etEmail = ViewBindings.findChildViewById(rootView, id);
      if (etEmail == null) {
        break missingId;
      }

      id = R.id.et_name;
      TextInputEditText etName = ViewBindings.findChildViewById(rootView, id);
      if (etName == null) {
        break missingId;
      }

      id = R.id.ll_buttons;
      LinearLayout llButtons = ViewBindings.findChildViewById(rootView, id);
      if (llButtons == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.til_email;
      TextInputLayout tilEmail = ViewBindings.findChildViewById(rootView, id);
      if (tilEmail == null) {
        break missingId;
      }

      id = R.id.til_name;
      TextInputLayout tilName = ViewBindings.findChildViewById(rootView, id);
      if (tilName == null) {
        break missingId;
      }

      id = R.id.tv_error;
      TextView tvError = ViewBindings.findChildViewById(rootView, id);
      if (tvError == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tv_user_info;
      TextView tvUserInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvUserInfo == null) {
        break missingId;
      }

      return new ActivityViewBindingBinding((ConstraintLayout) rootView, btnLoad, btnSave, etEmail,
          etName, llButtons, progressBar, tilEmail, tilName, tvError, tvTitle, tvUserInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
