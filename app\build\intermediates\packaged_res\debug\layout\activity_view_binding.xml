<?xml version="1.0" encoding="utf-8"?>
<!-- 布局示例 -->
<!-- 不需要<layout>标签，直接使用普通布局 -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ViewBindingActivity">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="ViewBinding示例"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp" />

    <!-- 用户名输入 -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="用户名"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="32dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPersonName" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 邮箱输入 -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_email"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="邮箱"
        app:layout_constraintTop_toBottomOf="@id/til_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textEmailAddress" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 用户信息显示 -->
    <TextView
        android:id="@+id/tv_user_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="用户信息将在这里显示"
        android:textSize="16sp"
        android:background="@android:color/darker_gray"
        android:textColor="@android:color/white"
        android:padding="12dp"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/til_email"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 按钮容器 -->
    <LinearLayout
        android:id="@+id/ll_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/tv_user_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 保存按钮 -->
        <Button
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="保存"
            android:layout_marginEnd="8dp" />

        <!-- 加载按钮 -->
        <Button
            android:id="@+id/btn_load"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="加载"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/ll_buttons"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="24dp" />

    <!-- 错误信息 -->
    <TextView
        android:id="@+id/tv_error"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="错误信息"
        android:textColor="@android:color/holo_red_dark"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/progress_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp" />

    <!-- 说明文本 -->
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="这是ViewBinding示例，相比DataBinding：\n• 不需要&lt;layout&gt;标签\n• 不支持数据绑定表达式\n• 需要手动观察数据变化\n• 更简单，更稳定"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginTop="16dp"
        android:padding="8dp"
        android:background="@android:color/background_light"
        app:layout_constraintTop_toBottomOf="@id/tv_error"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
